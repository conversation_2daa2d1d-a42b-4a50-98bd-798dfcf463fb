#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的文件夹删除功能测试脚本
演示如何使用 TrainingDataManager 的删除文件夹功能
"""

import boto3
from botocore.client import Config
import os
import sys

# 导入 TrainingDataManager 类
sys.path.append('.')
from testCeph import TrainingDataManager

# 配置S3客户端
s3 = boto3.client(
    's3',
    endpoint_url='http://192.168.1.40:7480',
    aws_access_key_id='59JGG325T15Y3O5M9MTE',
    aws_secret_access_key='S7YZQBTc9TgAmyxDie69pzCUpCc5LBDA1EmChb4S',
    config=Config(signature_version='s3v4')
)

def demo_delete_folder():
    """演示删除文件夹功能"""
    try:
        print("=== 文件夹删除功能演示 ===")
        
        # 创建训练数据管理器
        manager = TrainingDataManager(s3)
        
        # 1. 先上传一些测试文件到测试文件夹
        print("\n1. 创建测试数据:")
        test_files = [
            ("demo_file1.txt", "demo-folder/file1.txt", "演示文件1"),
            ("demo_file2.txt", "demo-folder/subfolder/file2.txt", "演示文件2"),
            ("demo_file3.txt", "demo-folder/data.csv", "name,value\ntest,123"),
        ]
        
        for local_file, object_key, content in test_files:
            # 创建本地测试文件
            with open(local_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 上传文件
            success = manager.upload_file(local_file, object_key)
            if success:
                print(f"  ✓ 上传成功: {object_key}")
            
            # 删除本地文件
            os.remove(local_file)
        
        # 2. 显示当前文件列表（只显示 demo-folder 前缀的文件）
        print("\n2. 查看测试文件夹内容:")
        all_files = manager.list_training_data()
        demo_files = [f for f in all_files if f['key'].startswith('demo-folder')]
        
        if demo_files:
            print("  demo-folder 中的文件:")
            for file_info in demo_files:
                print(f"    - {file_info['key']} ({file_info['size']} 字节)")
        else:
            print("  demo-folder 中没有文件")
        
        # 3. 演示删除文件夹功能
        print("\n3. 删除 demo-folder 文件夹:")
        delete_result = manager.delete_folder("demo-folder")
        
        print(f"  删除结果: 成功 {delete_result['success']} 个, 失败 {delete_result['failed']} 个")
        
        # 4. 验证删除结果
        print("\n4. 验证删除结果:")
        all_files_after = manager.list_training_data()
        demo_files_after = [f for f in all_files_after if f['key'].startswith('demo-folder')]
        
        if demo_files_after:
            print("  ❌ 删除不完整，仍有文件:")
            for file_info in demo_files_after:
                print(f"    - {file_info['key']}")
        else:
            print("  ✅ demo-folder 文件夹已完全删除")
        
        print("\n=== 文件夹删除功能演示完成 ===")
        
    except Exception as e:
        print(f"[错误] 演示失败: {str(e)}")

def demo_usage_examples():
    """显示使用示例"""
    print("\n=== 删除文件夹功能使用示例 ===")
    print()
    print("# 创建训练数据管理器")
    print("manager = TrainingDataManager(s3)")
    print()
    print("# 删除特定前缀的文件夹")
    print('result = manager.delete_folder("my-dataset")')
    print('print(f"删除了 {result[\'success\']} 个文件")')
    print()
    print("# 删除多层嵌套的文件夹")
    print('manager.delete_folder("datasets/training/batch1")')
    print()
    print("# 删除所有文件（危险操作）")
    print('manager.delete_all_files(confirm=True)')
    print()
    print("# 安全的批量删除示例")
    print("folders_to_delete = ['old-data', 'temp-files', 'backup-2023']")
    print("for folder in folders_to_delete:")
    print("    result = manager.delete_folder(folder)")
    print('    print(f"删除文件夹 {folder}: {result[\'success\']} 成功, {result[\'failed\']} 失败")')

if __name__ == "__main__":
    # 运行演示
    demo_delete_folder()
    
    # 显示使用示例
    demo_usage_examples()
