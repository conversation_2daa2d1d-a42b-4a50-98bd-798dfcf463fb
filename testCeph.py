import boto3
from botocore.client import Config

# 配置S3客户端
s3 = boto3.client(
    's3',
    endpoint_url='http://192.168.1.40:7480',  # 你的接口URL
    aws_access_key_id='59JGG325T15Y3O5M9MTE',    # access key
    aws_secret_access_key='S7YZQBTc9TgAmyxDie69pzCUpCc5LBDA1EmChb4S',  # secret key
    config=Config(signature_version='s3v4')  # 必须指定签名版本
)

def test_s3_interface():
    try:
        # 1. 列出所有存储桶（验证连接）
        buckets = s3.list_buckets()
        print("[连接成功] 存储桶列表:", [b['Name'] for b in buckets['Buckets']])

        # 2. 创建测试桶（若不存在）
        test_bucket = "test-bucket-python"
        if test_bucket not in [b['Name'] for b in buckets['Buckets']]:
            s3.create_bucket(Bucket=test_bucket)
            print(f"[创建桶] 成功: {test_bucket}")

        # 3. 上传测试文件
        object_key = "test-file.txt"
        s3.put_object(
            Bucket=test_bucket,
            Key=object_key,
            Body=b"Hello from Python S3 Test!"
        )
        print(f"[上传] 文件成功: {object_key}")

        # 4. 下载并验证文件
        response = s3.get_object(Bucket=test_bucket, Key=object_key)
        downloaded_data = response['Body'].read()
        assert downloaded_data == b"Hello from Python S3 Test!"
        print("[验证] 文件内容一致")

        # 5. 清理测试资源
        s3.delete_object(Bucket=test_bucket, Key=object_key)
        s3.delete_bucket(Bucket=test_bucket)
        print("[清理] 测试资源已删除")

    except Exception as e:
        print(f"[错误] 接口测试失败: {str(e)}")

if __name__ == "__main__":
    test_s3_interface()