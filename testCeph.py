import boto3
from botocore.client import Config
import os
from pathlib import Path

# 配置S3客户端
s3 = boto3.client(
    's3',
    endpoint_url='http://192.168.1.40:7480',  # 你的接口URL
    aws_access_key_id='59JGG325T15Y3O5M9MTE',    # access key
    aws_secret_access_key='S7YZQBTc9TgAmyxDie69pzCUpCc5LBDA1EmChb4S',  # secret key
    config=Config(signature_version='s3v4')  # 必须指定签名版本
)

class TrainingDataManager:
    """训练数据管理器，用于管理训练数据的上传、下载和管理"""

    def __init__(self, s3_client, bucket_name="training-data"):
        self.s3 = s3_client
        self.bucket_name = bucket_name
        self._ensure_bucket_exists()

    def _ensure_bucket_exists(self):
        """确保训练数据桶存在，如果不存在则创建"""
        try:
            buckets = self.s3.list_buckets()
            bucket_names = [b['Name'] for b in buckets['Buckets']]

            if self.bucket_name not in bucket_names:
                self.s3.create_bucket(Bucket=self.bucket_name)
                print(f"[创建桶] 训练数据桶创建成功: {self.bucket_name}")
            else:
                print(f"[桶状态] 训练数据桶已存在: {self.bucket_name}")
        except Exception as e:
            print(f"[错误] 创建训练数据桶失败: {str(e)}")
            raise

    def upload_file(self, file_path, object_key=None):
        """
        上传文件到训练数据桶

        Args:
            file_path (str): 本地文件路径
            object_key (str, optional): 对象键名，如果不指定则使用文件名

        Returns:
            bool: 上传是否成功
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"[错误] 文件不存在: {file_path}")
                return False

            # 如果没有指定对象键，使用文件名
            if object_key is None:
                object_key = os.path.basename(file_path)

            # 获取文件大小用于显示进度
            file_size = os.path.getsize(file_path)
            print(f"[上传开始] 文件: {file_path} -> {object_key} (大小: {file_size} 字节)")

            # 上传文件
            with open(file_path, 'rb') as file_data:
                self.s3.put_object(
                    Bucket=self.bucket_name,
                    Key=object_key,
                    Body=file_data
                )

            print(f"[上传成功] 文件已上传: {object_key}")
            return True

        except Exception as e:
            print(f"[上传失败] 文件: {file_path}, 错误: {str(e)}")
            return False

    def upload_multiple_files(self, file_paths):
        """
        批量上传多个文件

        Args:
            file_paths (list): 文件路径列表

        Returns:
            dict: 上传结果统计
        """
        results = {"success": 0, "failed": 0, "details": []}

        for file_path in file_paths:
            success = self.upload_file(file_path)
            if success:
                results["success"] += 1
                results["details"].append({"file": file_path, "status": "success"})
            else:
                results["failed"] += 1
                results["details"].append({"file": file_path, "status": "failed"})

        print(f"[批量上传完成] 成功: {results['success']}, 失败: {results['failed']}")
        return results

    def list_training_data(self):
        """列出训练数据桶中的所有文件"""
        try:
            response = self.s3.list_objects_v2(Bucket=self.bucket_name)

            if 'Contents' not in response:
                print(f"[数据列表] 训练数据桶为空: {self.bucket_name}")
                return []

            files = []
            print(f"[数据列表] 训练数据桶 {self.bucket_name} 中的文件:")
            for obj in response['Contents']:
                file_info = {
                    'key': obj['Key'],
                    'size': obj['Size'],
                    'last_modified': obj['LastModified']
                }
                files.append(file_info)
                print(f"  - {obj['Key']} (大小: {obj['Size']} 字节, 修改时间: {obj['LastModified']})")

            return files

        except Exception as e:
            print(f"[错误] 获取文件列表失败: {str(e)}")
            return []

    def download_file(self, object_key, local_path=None):
        """
        从训练数据桶下载文件

        Args:
            object_key (str): 对象键名
            local_path (str, optional): 本地保存路径，如果不指定则使用对象键名

        Returns:
            bool: 下载是否成功
        """
        try:
            if local_path is None:
                local_path = object_key

            print(f"[下载开始] {object_key} -> {local_path}")

            response = self.s3.get_object(Bucket=self.bucket_name, Key=object_key)

            # 创建目录（如果需要）
            os.makedirs(os.path.dirname(local_path) if os.path.dirname(local_path) else '.', exist_ok=True)

            with open(local_path, 'wb') as f:
                f.write(response['Body'].read())

            print(f"[下载成功] 文件已保存: {local_path}")
            return True

        except Exception as e:
            print(f"[下载失败] 对象: {object_key}, 错误: {str(e)}")
            return False

    def delete_file(self, object_key):
        """删除训练数据桶中的文件"""
        try:
            self.s3.delete_object(Bucket=self.bucket_name, Key=object_key)
            print(f"[删除成功] 文件已删除: {object_key}")
            return True
        except Exception as e:
            print(f"[删除失败] 对象: {object_key}, 错误: {str(e)}")
            return False

    def upload_folder(self, folder_path, prefix="", exclude_patterns=None):
        """
        上传整个文件夹到训练数据桶

        Args:
            folder_path (str): 本地文件夹路径
            prefix (str, optional): 对象键前缀，用于在桶中创建文件夹结构
            exclude_patterns (list, optional): 要排除的文件模式列表（如 ['*.tmp', '*.log']）

        Returns:
            dict: 上传结果统计
        """
        try:
            if not os.path.exists(folder_path):
                print(f"[错误] 文件夹不存在: {folder_path}")
                return {"success": 0, "failed": 0, "details": []}

            if not os.path.isdir(folder_path):
                print(f"[错误] 路径不是文件夹: {folder_path}")
                return {"success": 0, "failed": 0, "details": []}

            # 设置默认排除模式
            if exclude_patterns is None:
                exclude_patterns = ['*.tmp', '*.log', '*.cache', '__pycache__', '.DS_Store']

            results = {"success": 0, "failed": 0, "details": []}

            print(f"[文件夹上传开始] 扫描文件夹: {folder_path}")

            # 遍历文件夹中的所有文件
            for root, dirs, files in os.walk(folder_path):
                # 排除隐藏文件夹
                dirs[:] = [d for d in dirs if not d.startswith('.')]

                for file in files:
                    # 检查是否需要排除此文件
                    should_exclude = False
                    for pattern in exclude_patterns:
                        if self._match_pattern(file, pattern):
                            should_exclude = True
                            break

                    if should_exclude:
                        print(f"[跳过] 排除文件: {file}")
                        continue

                    # 构建完整的文件路径
                    file_path = os.path.join(root, file)

                    # 构建相对路径作为对象键
                    rel_path = os.path.relpath(file_path, folder_path)
                    # 将Windows路径分隔符转换为S3标准的斜杠
                    object_key = rel_path.replace(os.sep, '/')

                    # 添加前缀
                    if prefix:
                        object_key = f"{prefix.rstrip('/')}/{object_key}"

                    # 上传文件
                    success = self.upload_file(file_path, object_key)

                    if success:
                        results["success"] += 1
                        results["details"].append({
                            "file": file_path,
                            "object_key": object_key,
                            "status": "success"
                        })
                    else:
                        results["failed"] += 1
                        results["details"].append({
                            "file": file_path,
                            "object_key": object_key,
                            "status": "failed"
                        })

            print(f"[文件夹上传完成] 成功: {results['success']}, 失败: {results['failed']}")
            return results

        except Exception as e:
            print(f"[文件夹上传失败] 文件夹: {folder_path}, 错误: {str(e)}")
            return {"success": 0, "failed": 0, "details": []}

    def _match_pattern(self, filename, pattern):
        """简单的文件名模式匹配"""
        import fnmatch
        return fnmatch.fnmatch(filename, pattern)

def test_s3_interface():
    try:
        # 1. 列出所有存储桶（验证连接）
        buckets = s3.list_buckets()
        print("[连接成功] 存储桶列表:", [b['Name'] for b in buckets['Buckets']])

        # 2. 创建测试桶（若不存在）
        test_bucket = "test-bucket-python"
        if test_bucket not in [b['Name'] for b in buckets['Buckets']]:
            s3.create_bucket(Bucket=test_bucket)
            print(f"[创建桶] 成功: {test_bucket}")

        # 3. 上传测试文件
        object_key = "test-file.txt"
        s3.put_object(
            Bucket=test_bucket,
            Key=object_key,
            Body=b"Hello from Python S3 Test!"
        )
        print(f"[上传] 文件成功: {object_key}")

        # 4. 下载并验证文件
        response = s3.get_object(Bucket=test_bucket, Key=object_key)
        downloaded_data = response['Body'].read()
        assert downloaded_data == b"Hello from Python S3 Test!"
        print("[验证] 文件内容一致")

        # 5. 清理测试资源
        s3.delete_object(Bucket=test_bucket, Key=object_key)
        s3.delete_bucket(Bucket=test_bucket)
        print("[清理] 测试资源已删除")

    except Exception as e:
        print(f"[错误] 接口测试失败: {str(e)}")

def test_training_data_manager():
    """测试训练数据管理功能"""
    try:
        print("\n=== 训练数据管理功能测试 ===")

        # 创建训练数据管理器
        manager = TrainingDataManager(s3)

        # 创建测试文件
        test_file_path = "test_training_data.txt"
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write("这是一个测试训练数据文件\n包含一些示例数据用于测试上传功能")

        # 测试上传文件
        print("\n1. 测试文件上传:")
        success = manager.upload_file(test_file_path, "sample-training-data.txt")

        if success:
            # 测试列出文件
            print("\n2. 测试文件列表:")
            files = manager.list_training_data()

            # 测试下载文件
            print("\n3. 测试文件下载:")
            download_path = "downloaded_training_data.txt"
            manager.download_file("sample-training-data.txt", download_path)

            # 验证下载的文件
            if os.path.exists(download_path):
                with open(download_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"[验证] 下载文件内容: {content[:50]}...")
                os.remove(download_path)  # 清理下载的文件

            # 测试删除文件
            print("\n4. 测试文件删除:")
            manager.delete_file("sample-training-data.txt")

        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

        print("\n=== 训练数据管理功能测试完成 ===")

    except Exception as e:
        print(f"[错误] 训练数据管理测试失败: {str(e)}")

def test_folder_upload():
    """测试文件夹上传功能"""
    try:
        print("\n=== 文件夹上传功能测试 ===")

        # 创建训练数据管理器
        manager = TrainingDataManager(s3)

        # 创建测试文件夹结构
        test_folder = "test_training_folder"
        os.makedirs(test_folder, exist_ok=True)
        os.makedirs(os.path.join(test_folder, "subfolder"), exist_ok=True)

        # 创建测试文件
        test_files = [
            (os.path.join(test_folder, "data1.txt"), "训练数据文件1\n包含一些示例数据"),
            (os.path.join(test_folder, "data2.csv"), "name,value\ntest1,100\ntest2,200"),
            (os.path.join(test_folder, "subfolder", "nested_data.json"), '{"type": "training", "samples": 1000}'),
            (os.path.join(test_folder, "temp.tmp"), "临时文件，应该被排除"),  # 这个文件应该被排除
        ]

        for file_path, content in test_files:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

        print(f"[准备] 创建测试文件夹: {test_folder}")

        # 测试文件夹上传
        print("\n1. 测试文件夹上传:")
        results = manager.upload_folder(test_folder, prefix="test-dataset")

        if results["success"] > 0:
            # 列出上传的文件
            print("\n2. 查看上传的文件:")
            manager.list_training_data()

            # 清理上传的文件
            print("\n3. 清理测试文件:")
            for detail in results["details"]:
                if detail["status"] == "success":
                    manager.delete_file(detail["object_key"])

        # 清理本地测试文件夹
        import shutil
        if os.path.exists(test_folder):
            shutil.rmtree(test_folder)
            print(f"[清理] 删除测试文件夹: {test_folder}")

        print("\n=== 文件夹上传功能测试完成 ===")

    except Exception as e:
        print(f"[错误] 文件夹上传测试失败: {str(e)}")

def demo_training_data_usage():
    """演示如何使用训练数据管理功能"""
    print("\n=== 训练数据管理使用示例 ===")
    print("# 创建训练数据管理器")
    print("manager = TrainingDataManager(s3)")
    print()
    print("# 上传单个文件")
    print('manager.upload_file("/path/to/your/training_data.csv")')
    print()
    print("# 批量上传文件")
    print('files = ["/path/to/data1.csv", "/path/to/data2.json"]')
    print("manager.upload_multiple_files(files)")
    print()
    print("# 上传整个文件夹")
    print('manager.upload_folder("/path/to/training_dataset", prefix="my-dataset")')
    print()
    print("# 上传文件夹并排除特定文件")
    print('manager.upload_folder("/path/to/data", exclude_patterns=["*.tmp", "*.log", "*.cache"])')
    print()
    print("# 列出所有训练数据")
    print("manager.list_training_data()")
    print()
    print("# 下载文件")
    print('manager.download_file("training_data.csv", "local_copy.csv")')
    print()
    print("# 删除文件")
    print('manager.delete_file("old_data.csv")')

if __name__ == "__main__":
    # 运行原有的S3接口测试
    test_s3_interface()

    # 运行训练数据管理功能测试
    test_training_data_manager()

    # 运行文件夹上传功能测试
    test_folder_upload()

    # 显示使用示例
    demo_training_data_usage()

    # 创建管理器
    #manager = TrainingDataManager(s3)
    # 上传训练数据（支持文件路径参数）
    #manager.upload_file("E:\data\Wednesday-workingHours.pcap_ISCX.pkl")
    #manager.upload_file("E:\data\Flowers Recognition\flowers_dataset.pkl")

    # 批量上传
    #files = ["/path/to/data1.csv", "/path/to/data2.json"] 
    #manager.upload_multiple_files(files)

    # 批量上传
    #files = ["/path/to/data1.csv", "/path/to/data2.json"] 
    #manager.upload_multiple_files(files)

    # 上传整个文件夹
    #manager.upload_folder("E:\data\VOCdevkit\VOC2007", prefix="VOC2007")

    #manager.upload_folder("E:\data\Flowers Recognition/flowers", prefix="flowers")

    # 上传文件夹并自定义排除规则
    #manager.upload_folder("E:/data", exclude_patterns=["*.tmp", "*.log", "*.backup"])
